import demo.model.Person;
import demo.service.PersonService;
import java.util.concurrent.StructuredTaskScope;

void main() {
    final PersonService personService = new PersonService();

    try (var scope = StructuredTaskScope.open(StructuredTaskScope.Joiner.awaitAllSuccessfulOrThrow())) {
        var time = System.currentTimeMillis();
        var name = scope.fork(() -> personService.name());
        var age = scope.fork(() -> personService.age());
        scope.join();

        var person = new Person(name.get(), age.get());
        System.out.println("Total time: " + (System.currentTimeMillis() - time) + "ms");
        System.out.println(person);
    } catch (Exception e) {
        e.printStackTrace();
    }
}
